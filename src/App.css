/* Kong-inspired design system */
:root {
  --kong-blue: #1155cb;
  --kong-purple: #6366f1;
  --kong-dark-blue: #0f172a;
  --kong-light-blue: #3b82f6;
  --kong-gray-50: #f8fafc;
  --kong-gray-100: #f1f5f9;
  --kong-gray-200: #e2e8f0;
  --kong-gray-300: #cbd5e1;
  --kong-gray-600: #475569;
  --kong-gray-700: #334155;
  --kong-gray-900: #0f172a;
  --kong-success: #10b981;
  --kong-error: #ef4444;
  --kong-warning: #f59e0b;
}

* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
  background-color: var(--kong-gray-50);
}

.App {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--kong-gray-50) 0%, #ffffff 100%);
  color: var(--kong-gray-900);
}

.App-header {
  background: linear-gradient(135deg, var(--kong-blue) 0%, var(--kong-purple) 100%);
  padding: 4rem 2rem;
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.App-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.App-header h1 {
  margin: 0 0 1rem 0;
  font-size: 3.5rem;
  font-weight: 800;
  letter-spacing: -0.025em;
  position: relative;
  z-index: 1;
}

.App-header p {
  margin: 0;
  font-size: 1.25rem;
  opacity: 0.9;
  font-weight: 400;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
  position: relative;
  z-index: 1;
}

.App-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem 4rem;
  position: relative;
  top: -2rem;
}

.generation-form {
  background: white;
  border-radius: 24px;
  padding: 3rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--kong-gray-200);
  margin-bottom: 3rem;
  transition: all 0.3s ease;
}

.generation-form:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.input-group {
  margin-bottom: 2rem;
}

.input-group label {
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: var(--kong-gray-700);
  text-align: left;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.input-group textarea {
  width: 100%;
  padding: 1.25rem;
  border: 2px solid var(--kong-gray-200);
  border-radius: 12px;
  font-size: 1rem;
  font-family: inherit;
  resize: vertical;
  transition: all 0.2s ease;
  background-color: var(--kong-gray-50);
  min-height: 120px;
}

.input-group textarea:focus {
  outline: none;
  border-color: var(--kong-blue);
  box-shadow: 0 0 0 3px rgba(17, 85, 203, 0.1);
  background-color: white;
}

.input-group textarea:disabled {
  background-color: var(--kong-gray-100);
  cursor: not-allowed;
  opacity: 0.6;
}

.options-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.option {
  text-align: left;
}

.option label {
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: var(--kong-gray-700);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.option select {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--kong-gray-200);
  border-radius: 12px;
  font-size: 1rem;
  background-color: var(--kong-gray-50);
  transition: all 0.2s ease;
  cursor: pointer;
}

.option select:focus {
  outline: none;
  border-color: var(--kong-blue);
  box-shadow: 0 0 0 3px rgba(17, 85, 203, 0.1);
  background-color: white;
}

.option select:disabled {
  background-color: var(--kong-gray-100);
  cursor: not-allowed;
  opacity: 0.6;
}

.generate-button {
  background: linear-gradient(135deg, var(--kong-blue) 0%, var(--kong-purple) 100%);
  color: white;
  border: none;
  padding: 1.25rem 2.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 200px;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.generate-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.generate-button:hover:not(:disabled)::before {
  left: 100%;
}

.generate-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 10px 15px -3px rgba(17, 85, 203, 0.3), 0 4px 6px -2px rgba(17, 85, 203, 0.05);
}

.generate-button:active:not(:disabled) {
  transform: translateY(0);
}

.generate-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  background: var(--kong-gray-300);
}

.error-message {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  color: var(--kong-error);
  border-left: 4px solid var(--kong-error);
}

.error-message p {
  margin: 0;
  font-weight: 500;
}

.results-section {
  background: white;
  border-radius: 24px;
  padding: 3rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--kong-gray-200);
  transition: all 0.3s ease;
}

.results-section:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.results-section h2 {
  margin-top: 0;
  margin-bottom: 2rem;
  color: var(--kong-gray-900);
  font-size: 2rem;
  font-weight: 700;
  text-align: center;
}

.image-result {
  margin-bottom: 3rem;
  text-align: center;
}

.image-result img {
  max-width: 100%;
  height: auto;
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  margin-bottom: 2rem;
  transition: all 0.3s ease;
}

.image-result img:hover {
  transform: scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.revised-prompt {
  text-align: left;
  background: var(--kong-gray-50);
  padding: 1.5rem;
  border-radius: 16px;
  border-left: 4px solid var(--kong-blue);
  margin-top: 1rem;
}

.revised-prompt h3 {
  margin: 0 0 0.75rem 0;
  color: var(--kong-gray-700);
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.revised-prompt p {
  margin: 0;
  color: var(--kong-gray-600);
  font-style: italic;
  line-height: 1.6;
}

/* Loading animation */
.generate-button:disabled::after {
  content: '';
  width: 16px;
  height: 16px;
  margin-left: 8px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  display: inline-block;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 1024px) {
  .App-main {
    padding: 0 1.5rem 4rem;
  }
}

@media (max-width: 768px) {
  .App-header {
    padding: 3rem 1.5rem;
  }

  .App-header h1 {
    font-size: 2.5rem;
  }

  .App-header p {
    font-size: 1.125rem;
  }

  .generation-form,
  .results-section {
    padding: 2rem;
    margin: 0 1rem 3rem;
  }

  .options-group {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .generate-button {
    width: 100%;
    padding: 1rem 2rem;
  }
}

@media (max-width: 480px) {
  .App-header {
    padding: 2rem 1rem;
  }

  .App-header h1 {
    font-size: 2rem;
  }

  .generation-form,
  .results-section {
    padding: 1.5rem;
    margin: 0 0.5rem 2rem;
  }

  .input-group textarea {
    min-height: 100px;
  }
}
