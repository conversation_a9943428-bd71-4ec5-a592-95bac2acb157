import OpenAI from 'openai';

// Initialize OpenAI client
// Uses custom baseURL if REACT_APP_OPENAI_BASE_URL is set, otherwise uses default OpenAI endpoint
const openai = new OpenAI({
  baseURL: process.env.REACT_APP_OPENAI_BASE_URL || undefined,
  apiKey: process.env.REACT_APP_OPENAI_API_KEY,
  dangerouslyAllowBrowser: true // Note: In production, API calls should be made from a backend server
});

export interface ImageGenerationOptions {
  prompt: string;
  size?: '1024x1024' | '1792x1024' | '1024x1792';
  quality?: 'standard' | 'hd';
  n?: number;
}

export interface GeneratedImage {
  url: string;
  revisedPrompt?: string;
}

export class ImageGenerationService {
  /**
   * Generate images using OpenAI's DALL-E model
   *
   * Configuration:
   * - REACT_APP_OPENAI_API_KEY: Required OpenAI API key
   * - REACT_APP_OPENAI_BASE_URL: Optional custom base URL (defaults to OpenAI's endpoint)
   */
  static async generateImage(options: ImageGenerationOptions): Promise<GeneratedImage[]> {
    try {
      const {
        prompt,
        size = '1024x1024',
        quality = 'standard',
        n = 1
      } = options;

      if (!process.env.REACT_APP_OPENAI_API_KEY) {
        throw new Error('OpenAI API key is not configured. Please set REACT_APP_OPENAI_API_KEY in your .env file.');
      }

      if (!prompt.trim()) {
        throw new Error('Prompt cannot be empty');
      }

      const response = await openai.images.generate({
        model: 'dall-e-3',
        prompt: prompt.trim(),
        size,
        quality,
        n,
      });

      return (response.data || []).map(image => ({
        url: image.url || '',
        revisedPrompt: image.revised_prompt
      }));
    } catch (error) {
      console.error('Error generating image:', error);
      
      if (error instanceof Error) {
        throw new Error(`Failed to generate image: ${error.message}`);
      }
      
      throw new Error('Failed to generate image: Unknown error occurred');
    }
  }

  /**
   * Validate if the API key is configured
   */
  static isConfigured(): boolean {
    return !!process.env.REACT_APP_OPENAI_API_KEY && 
           process.env.REACT_APP_OPENAI_API_KEY !== 'your_openai_api_key_here';
  }
}
