import React, { useState } from 'react';
import './App.css';
import { ImageGenerationService, ImageGenerationOptions, GeneratedImage } from './services/imageGeneration';

function App() {
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImages, setGeneratedImages] = useState<GeneratedImage[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [imageSize, setImageSize] = useState<'1024x1024' | '1792x1024' | '1024x1792'>('1024x1024');
  const [imageQuality, setImageQuality] = useState<'standard' | 'hd'>('standard');

  const handleGenerateImage = async () => {
    if (!prompt.trim()) {
      setError('Please enter a prompt');
      return;
    }

    if (!ImageGenerationService.isConfigured()) {
      setError('OpenAI API key is not configured. Please set your API key in the .env file.');
      return;
    }

    setIsGenerating(true);
    setError(null);
    setGeneratedImages([]);

    try {
      const options: ImageGenerationOptions = {
        prompt,
        size: imageSize,
        quality: imageQuality,
        n: 1
      };

      const images = await ImageGenerationService.generateImage(options);
      setGeneratedImages(images);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while generating the image');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleGenerateImage();
    }
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>AI Image Generator</h1>
        <p>Generate stunning images from text descriptions using OpenAI's DALL-E</p>
      </header>

      <main className="App-main">
        <div className="generation-form">
          <div className="input-group">
            <label htmlFor="prompt">Describe the image you want to generate:</label>
            <textarea
              id="prompt"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="e.g., A serene landscape with mountains and a lake at sunset"
              rows={3}
              disabled={isGenerating}
            />
          </div>

          <div className="options-group">
            <div className="option">
              <label htmlFor="size">Size:</label>
              <select
                id="size"
                value={imageSize}
                onChange={(e) => setImageSize(e.target.value as any)}
                disabled={isGenerating}
              >
                <option value="1024x1024">1024x1024 (Square)</option>
                <option value="1792x1024">1792x1024 (Landscape)</option>
                <option value="1024x1792">1024x1792 (Portrait)</option>
              </select>
            </div>

            <div className="option">
              <label htmlFor="quality">Quality:</label>
              <select
                id="quality"
                value={imageQuality}
                onChange={(e) => setImageQuality(e.target.value as any)}
                disabled={isGenerating}
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
              </select>
            </div>
          </div>

          <button
            onClick={handleGenerateImage}
            disabled={isGenerating || !prompt.trim()}
            className="generate-button"
          >
            {isGenerating ? 'Generating...' : 'Generate Image'}
          </button>
        </div>

        {error && (
          <div className="error-message">
            <p>{error}</p>
          </div>
        )}

        {generatedImages.length > 0 && (
          <div className="results-section">
            <h2>Generated Image</h2>
            {generatedImages.map((image, index) => (
              <div key={index} className="image-result">
                <img src={image.url} alt={`Generated: ${prompt}`} />
                {image.revisedPrompt && (
                  <div className="revised-prompt">
                    <h3>Revised Prompt:</h3>
                    <p>{image.revisedPrompt}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </main>
    </div>
  );
}

export default App;
