# AI Image Generation Demo

This is a React application that generates images from text prompts using OpenAI's DALL-E API. The app provides an intuitive interface for creating stunning AI-generated images with customizable options.

## Features

- **Text-to-Image Generation**: Generate images from descriptive text prompts
- **Customizable Options**: Choose image size, style (vivid/natural), and quality (standard/HD)
- **Responsive Design**: Works on desktop and mobile devices
- **Real-time Feedback**: Shows loading states and error messages
- **Revised Prompts**: Displays how OpenAI interpreted and refined your prompt

## Setup Instructions

### 1. Install Dependencies

```bash
npm install
```

### 2. Configure OpenAI API Key

1. Get your OpenAI API key from [OpenAI Platform](https://platform.openai.com/api-keys)
2. Copy the `.env.example` file to `.env`:
   ```bash
   cp .env.example .env
   ```
3. Edit the `.env` file and replace `your_openai_api_key_here` with your actual API key:
   ```
   REACT_APP_OPENAI_API_KEY=sk-your-actual-api-key-here
   ```

### 3. Start the Development Server

```bash
npm start
```

The app will open in your browser at [http://localhost:3000](http://localhost:3000) (or another port if 3000 is busy).

## Usage

1. **Enter a Prompt**: Describe the image you want to generate in the text area
2. **Choose Options**: Select your preferred image size, style, and quality
3. **Generate**: Click the "Generate Image" button
4. **View Results**: The generated image will appear below with the revised prompt

### Example Prompts

- "A serene landscape with mountains and a lake at sunset"
- "A futuristic city with flying cars and neon lights"
- "A cute robot playing with a cat in a garden"
- "An abstract painting with vibrant colors and geometric shapes"

## Important Notes

- **API Costs**: Each image generation request costs credits from your OpenAI account
- **Rate Limits**: OpenAI has rate limits on API usage
- **Browser Usage**: This demo runs the OpenAI API directly in the browser for simplicity. In production, API calls should be made from a secure backend server
- **Environment Variables**: Never commit your actual API key to version control

## Technologies Used

- React 19 with TypeScript
- OpenAI SDK
- CSS3 with modern styling
- Create React App

## Available Scripts

In the project directory, you can run:

### `npm start`

Runs the app in the development mode.\
Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

The page will reload if you make edits.\
You will also see any lint errors in the console.

### `npm test`

Launches the test runner in the interactive watch mode.\
See the section about [running tests](https://facebook.github.io/create-react-app/docs/running-tests) for more information.

### `npm run build`

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

See the section about [deployment](https://facebook.github.io/create-react-app/docs/deployment) for more information.

### `npm run eject`

**Note: this is a one-way operation. Once you `eject`, you can’t go back!**

If you aren’t satisfied with the build tool and configuration choices, you can `eject` at any time. This command will remove the single build dependency from your project.

Instead, it will copy all the configuration files and the transitive dependencies (webpack, Babel, ESLint, etc) right into your project so you have full control over them. All of the commands except `eject` will still work, but they will point to the copied scripts so you can tweak them. At this point you’re on your own.

You don’t have to ever use `eject`. The curated feature set is suitable for small and middle deployments, and you shouldn’t feel obligated to use this feature. However we understand that this tool wouldn’t be useful if you couldn’t customize it when you are ready for it.

## Learn More

You can learn more in the [Create React App documentation](https://facebook.github.io/create-react-app/docs/getting-started).

To learn React, check out the [React documentation](https://reactjs.org/).
